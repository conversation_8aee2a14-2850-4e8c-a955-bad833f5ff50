# Aggregated Retrieved Documents for Debugging

## Overview

While we removed the streaming references system from the frontend, we now aggregate and store deduplicated retrieved documents in the database's `retrievedDocuments` field for debugging and analytics purposes.

## Implementation

### Document Aggregation Function

```typescript
/**
 * Aggregates and deduplicates retrieved documents from agent steps for debugging purposes
 */
function aggregateRetrievedDocuments(agentSteps: AgentStep[]): unknown[] {
  const documentMap = new Map<string, Record<string, unknown>>();

  for (const step of agentSteps) {
    // Process retrieved chunks
    if (step.retrievedChunks) {
      for (const chunk of step.retrievedChunks) {
        const key = `chunk_${chunk.id}_${chunk.chunkId}`;
        if (!documentMap.has(key)) {
          documentMap.set(key, {
            type: 'chunk',
            id: chunk.id,
            chunkId: chunk.chunkId,
            fileName: chunk.fileName,
            key: chunk.key,
            fileType: chunk.fileType,
            similarity: chunk.similarity,
            metadata: chunk.metadata,
          });
        }
      }
    }

    // Process retrieved summaries
    if (step.retrievedSummaries) {
      for (const summary of step.retrievedSummaries) {
        const key = `summary_${summary.id}`;
        if (!documentMap.has(key)) {
          documentMap.set(key, {
            type: 'summary',
            id: summary.id,
            fileName: summary.fileName,
            key: summary.key,
            fileType: summary.fileType,
            similarity: summary.similarity,
          });
        }
      }
    }
  }

  return Array.from(documentMap.values());
}
```

### Database Storage

```typescript
// Store the interaction in the database
await ChatRepository.storeInteraction({
  topicId: chatId,
  userId: session.user?.id ?? '',
  content: finalAnswer,
  senderType: 'ai',
  retrievedDocuments: aggregatedDocuments, // Aggregated documents for debugging
  agentSteps: progressUpdates,
});
```

### Updated Type Definitions

Enhanced the `AgentStep` type to include the `key` field:

```typescript
// Retrieval results (retrieve_context, evaluate_results, generate_answer)
retrievedChunks?: Array<{
  id: string;
  chunkId: number;
  fileName: string;
  content: string; // Truncated for storage efficiency
  key: string; // S3 key for document viewer
  similarity?: number;
  fileType: string;
  metadata: DocumentMetadata;
}>;
retrievedSummaries?: Array<{
  id: string;
  fileName: string;
  summary: string; // Truncated for storage efficiency
  key: string; // S3 key for document viewer
  similarity?: number;
  fileType: string; // File type for consistency
}>;
```

## Benefits

### 1. Debugging & Analytics
- **Easy inspection** - See exactly which documents were retrieved for each query
- **Performance analysis** - Track which documents are most frequently retrieved
- **Quality assessment** - Analyze similarity scores and retrieval patterns
- **Issue diagnosis** - Debug retrieval problems with complete document metadata

### 2. Data Consistency
- **Schema preservation** - Maintains the intended use of `retrievedDocuments` field
- **Structured data** - Clean, consistent format for all retrieved documents
- **Deduplication** - Eliminates duplicate documents across multiple retrieval steps
- **Type safety** - Proper TypeScript types for all document fields

### 3. Future Flexibility
- **Analytics ready** - Data available for future analytics features
- **API potential** - Could expose aggregated documents via API if needed
- **Migration support** - Maintains compatibility with existing database schema
- **Audit trail** - Complete record of what documents influenced each response

## Data Structure

### Aggregated Document Format

Each document in the `retrievedDocuments` array has this structure:

```typescript
// For chunks
{
  type: 'chunk',
  id: string,           // Document ID
  chunkId: number,      // Chunk ID
  fileName: string,     // Original filename
  key: string,          // S3 key for document access
  fileType: string,     // File type (pdf, docx, etc.)
  similarity: number,   // Similarity score
  metadata: object      // Page number, etc.
}

// For summaries
{
  type: 'summary',
  id: string,           // Document ID
  fileName: string,     // Original filename
  key: string,          // S3 key for document access
  fileType: string,     // File type
  similarity: number    // Similarity score
}
```

### Deduplication Logic

- **Chunks**: Deduplicated by `chunk_${id}_${chunkId}`
- **Summaries**: Deduplicated by `summary_${id}`
- **Cross-type**: Chunks and summaries from the same document are kept separate

## Usage Examples

### Database Query for Debugging

```sql
-- Get all retrieved documents for recent interactions
SELECT 
  content,
  retrieved_documents,
  created_at
FROM interactions 
WHERE sender_type = 'ai' 
  AND retrieved_documents IS NOT NULL
ORDER BY created_at DESC 
LIMIT 10;
```

### Analytics Queries

```sql
-- Most frequently retrieved documents
SELECT 
  doc->>'fileName' as file_name,
  doc->>'type' as doc_type,
  COUNT(*) as retrieval_count
FROM interactions,
     jsonb_array_elements(retrieved_documents) as doc
WHERE sender_type = 'ai'
  AND retrieved_documents IS NOT NULL
GROUP BY doc->>'fileName', doc->>'type'
ORDER BY retrieval_count DESC;

-- Average similarity scores by document
SELECT 
  doc->>'fileName' as file_name,
  AVG((doc->>'similarity')::float) as avg_similarity,
  COUNT(*) as retrieval_count
FROM interactions,
     jsonb_array_elements(retrieved_documents) as doc
WHERE sender_type = 'ai'
  AND retrieved_documents IS NOT NULL
  AND doc->>'similarity' IS NOT NULL
GROUP BY doc->>'fileName'
ORDER BY avg_similarity DESC;
```

## Implementation Notes

### Performance Considerations
- **Minimal overhead** - Aggregation happens only once per interaction
- **Efficient deduplication** - Uses Map for O(1) lookup performance
- **Compact storage** - Only essential fields stored, content truncated in agent steps

### Error Handling
- **Graceful degradation** - If aggregation fails, stores empty array
- **Type safety** - Proper TypeScript types prevent runtime errors
- **Validation** - Checks for required fields before processing

### Maintenance
- **No frontend impact** - Changes are backend-only
- **Schema compatible** - Uses existing `retrievedDocuments` field
- **Backward compatible** - Doesn't affect existing data

## Future Enhancements

### Analytics Dashboard
- **Retrieval patterns** - Visualize which documents are most useful
- **Performance metrics** - Track retrieval quality over time
- **User insights** - Understand how users interact with different document types

### API Endpoints
- **Document analytics** - Expose retrieval statistics via API
- **Debug endpoints** - Provide debugging information for support
- **Export functionality** - Allow exporting retrieval data for analysis

### Advanced Features
- **Similarity thresholds** - Track documents below certain similarity scores
- **Retrieval optimization** - Use data to improve retrieval algorithms
- **Content recommendations** - Suggest documents based on retrieval patterns

## Summary

The aggregated retrieved documents feature provides:

- ✅ **Complete debugging visibility** - See all documents that influenced each response
- ✅ **Analytics foundation** - Data ready for future analytics features
- ✅ **Schema consistency** - Proper use of existing database fields
- ✅ **Performance optimized** - Minimal overhead with efficient deduplication
- ✅ **Type safe** - Full TypeScript support with proper type definitions

This enhancement maintains the clean frontend experience while providing valuable debugging and analytics capabilities for the backend.
