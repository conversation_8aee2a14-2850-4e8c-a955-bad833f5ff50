'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChatEnhanced } from '@/hooks/use-chat-enhanced';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import { DocumentType } from '@/types/material';
import { Bot, Send, Settings2, X } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { useEffect, useMemo, useRef, useState } from 'react';
import { DocumentViewer } from '../documents/document-viewer';
import { AgentProgress } from './agent-progress';
import { ChatSettings } from './chat-settings';

type ChatUIEnhancedProps = {
  initialTopic: TopicWithDocuments;
  materialAccessUrl: string;
};

export function ChatUIEnhanced({ initialTopic, materialAccessUrl }: ChatUIEnhancedProps) {
  const { selectedReference, setSelectedReference } = useReferenceStore();
  const [message, setMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true); // Track if user is at bottom

  const { messages, error, sendMessage, isThinking, isAgentWorking, currentRunningStep } =
    useChatEnhanced(initialTopic);

  // console.log('agentSteps', agentSteps);

  const hasTriggeredInitialMessage = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user is at bottom of scroll container
  const checkIfUserAtBottom = () => {
    if (!scrollContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
    const threshold = 20; // 20px threshold for "near bottom"
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Handle scroll events to track user position
  const handleScroll = () => {
    setIsUserAtBottom(checkIfUserAtBottom());
  };

  // Smart auto scroll - only scroll when user is at bottom
  useEffect(() => {
    if (isUserAtBottom) {
      if (isThinking) {
        // Scroll to progress when agent starts working
        progressRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        // Scroll to bottom when messages are updated
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [messages, isThinking, isUserAtBottom]);

  // Ensure we're at bottom when new messages start
  useEffect(() => {
    if (isAgentWorking && isUserAtBottom) {
      setIsUserAtBottom(true);
    }
  }, [isAgentWorking, isUserAtBottom]);

  useEffect(() => {
    if (initialTopic.interactions.length > 1) return;
    if (hasTriggeredInitialMessage.current) return;

    const shouldTriggerAIResponse =
      initialTopic.interactions.length === 1 && initialTopic.interactions[0].senderType === 'user';

    if (shouldTriggerAIResponse) {
      hasTriggeredInitialMessage.current = true;
      sendMessage(initialTopic.interactions[0].content, { isInitialMessage: true });
    }
  }, [initialTopic.interactions, sendMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isAgentWorking) return;

    // Ensure we're at bottom when sending new message
    setIsUserAtBottom(true);
    await sendMessage(message);
    setMessage('');
  };

  const documentViewer = useMemo(
    () => (
      <div
        className={cn(
          'relative h-full w-1/2 overflow-y-auto border-l bg-gray-50 dark:bg-gray-900',
          selectedReference ? 'block' : 'hidden'
        )}
      >
        <DocumentViewer
          url={selectedReference ? `${materialAccessUrl}/${selectedReference.key}` : ''}
          fileType={selectedReference?.fileType || DocumentType.PDF}
          options={{
            initialPage: selectedReference?.metadata?.pageNumber
              ? Number(selectedReference.metadata.pageNumber)
              : undefined,
            timestamp: selectedReference?.metadata?.timestamp,
          }}
        />
        <div
          className="absolute bottom-4 right-4 cursor-pointer rounded-full bg-black p-3 text-white opacity-80 shadow-lg transition-all duration-200 hover:bg-gray-700"
          onClick={() => setSelectedReference(null)}
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close document viewer</span>
        </div>
      </div>
    ),
    [selectedReference, setSelectedReference, materialAccessUrl]
  );

  return (
    <div className="flex h-[calc(100vh-4.1rem)] w-full overflow-hidden bg-white dark:bg-gray-900">
      <div className={cn('flex flex-col', selectedReference ? 'w-1/2' : 'w-full')}>
        <div ref={scrollContainerRef} className="flex-1 overflow-y-auto" onScroll={handleScroll}>
          <div className="space-y-4 p-4">
            {messages.map((msg, i) => {
              return (
                <div key={i}>
                  {/* User Message */}
                  {msg.senderType === 'user' && (
                    <div className="flex justify-end">
                      <div className="ml-auto max-w-[80%] rounded-2xl bg-blue-500 px-4 py-3 text-white shadow-sm transition-all duration-300">
                        <div className="flex items-center space-x-2">
                          <span>{msg.content}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* AI Message with Integrated Progress */}
                  {msg.senderType === 'ai' && (
                    <div className="space-y-3">
                      {/* Show Agent Progress Above AI Message */}
                      {((msg.agentSteps && msg.agentSteps.length > 0) ||
                        (i === messages.length - 1 && currentRunningStep)) && (
                        <div className="flex justify-start">
                          <div className="w-full md:w-[80%] lg:max-w-[55%]">
                            <AgentProgress
                              steps={msg.agentSteps || []}
                              isThinking={
                                msg.isStreamingContent === true ||
                                (i === messages.length - 1 && !!currentRunningStep)
                              }
                              currentRunningStep={
                                i === messages.length - 1 ? currentRunningStep : null
                              }
                              autoExpanded={true} // Always expanded by default
                            />
                          </div>
                        </div>
                      )}

                      {/* AI Message Content */}
                      {msg.content && (
                        <div className="flex justify-start">
                          <div className="prose prose-sm max-w-[85%] rounded-2xl bg-gray-100 px-4 py-0 shadow-sm transition-all duration-300 dark:prose-invert dark:bg-gray-800 dark:text-gray-100">
                            <div className="[&_li]:my-1 [&_ol]:my-2 [&_p+ol]:mt-2 [&_p+ul]:mt-2 [&_p]:my-2 [&_ul]:my-2">
                              {/* AI Avatar */}
                              <div className="mb-3 flex items-center space-x-2 pt-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                                  <Bot className="h-3 w-3 text-white" />
                                </div>
                                <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                                  AI Assistant
                                </span>
                              </div>
                              <ReactMarkdown
                                components={{
                                  // Enhanced markdown rendering with footnote support
                                  p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                                  strong: ({ children }) => (
                                    <strong className="font-semibold text-gray-900 dark:text-gray-100">
                                      {children}
                                    </strong>
                                  ),
                                }}
                              >
                                {msg.content}
                              </ReactMarkdown>
                            </div>

                            {/* References are now handled by agent progress badges */}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Error Display */}
            {error && (
              <div className="flex justify-start">
                <div className="max-w-[85%] rounded-2xl border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-red-500">
                      <X className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Error: {error.message}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Enhanced Input Area */}
        <div className="border-t bg-white p-4 dark:bg-gray-900">
          <form onSubmit={handleSubmit}>
            <div className="flex items-end gap-3">
              <div className="flex-grow">
                <div className="relative">
                  <Input
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder={
                      isAgentWorking
                        ? 'AI is processing...'
                        : 'Ask me anything about your documents...'
                    }
                    disabled={isAgentWorking}
                    className={cn(
                      'rounded-xl border-2 py-3 pr-12 transition-all duration-200',
                      'focus:border-blue-400 focus:ring-2 focus:ring-blue-100',
                      isAgentWorking && 'cursor-not-allowed bg-gray-50 dark:bg-gray-800'
                    )}
                  />

                  {/* Settings Button */}
                  <Popover open={showSettings} onOpenChange={setShowSettings}>
                    <PopoverTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 hover:bg-gray-100 dark:hover:bg-gray-700"
                        disabled={isAgentWorking}
                      >
                        <Settings2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="end">
                      <ChatSettings />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Enhanced Send Button */}
              <Button
                type="submit"
                disabled={isAgentWorking || !message.trim()}
                className={cn(
                  'rounded-xl px-6 py-3 transition-all duration-200',
                  'bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-700',
                  isAgentWorking && 'cursor-not-allowed'
                )}
              >
                <Send className="mr-2 h-4 w-4" />
                {isAgentWorking ? 'Processing...' : 'Send'}
              </Button>
            </div>
          </form>
        </div>
      </div>

      {documentViewer}
    </div>
  );
}
