'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChatEnhanced } from '@/hooks/use-chat-enhanced';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import { DocumentType } from '@/types/material';
import { Bot, Send, Plus, Video, Search, Palette, X, Settings2 } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useEffect, useMemo, useRef, useState } from 'react';
import { DocumentViewer } from '../documents/document-viewer';
import { AgentProgress } from './agent-progress';
import { ChatSettings } from './chat-settings';

type ChatUIEnhancedProps = {
  initialTopic: TopicWithDocuments;
  materialAccessUrl: string;
};

// Utility function to format timestamps
const formatTimestamp = (date: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;

  // For older messages, show date and time
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export function ChatUIEnhanced({ initialTopic, materialAccessUrl }: ChatUIEnhancedProps) {
  const { selectedReference, setSelectedReference } = useReferenceStore();
  const [message, setMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true); // Track if user is at bottom

  const { messages, error, sendMessage, isThinking, isAgentWorking, currentRunningStep } =
    useChatEnhanced(initialTopic);



  // console.log('agentSteps', agentSteps);

  const hasTriggeredInitialMessage = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user is at bottom of scroll container
  const checkIfUserAtBottom = () => {
    if (!scrollContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
    const threshold = 20; // 20px threshold for "near bottom"
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Handle scroll events to track user position
  const handleScroll = () => {
    setIsUserAtBottom(checkIfUserAtBottom());
  };

  // Smart auto scroll - only scroll when user is at bottom
  useEffect(() => {
    if (isUserAtBottom) {
      if (isThinking) {
        // Scroll to progress when agent starts working
        progressRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        // Scroll to bottom when messages are updated
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [messages, isThinking, isUserAtBottom]);

  // Ensure we're at bottom when new messages start
  useEffect(() => {
    if (isAgentWorking && isUserAtBottom) {
      setIsUserAtBottom(true);
    }
  }, [isAgentWorking, isUserAtBottom]);

  useEffect(() => {
    if (initialTopic.interactions.length > 1) return;
    if (hasTriggeredInitialMessage.current) return;

    const shouldTriggerAIResponse =
      initialTopic.interactions.length === 1 && initialTopic.interactions[0].senderType === 'user';

    if (shouldTriggerAIResponse) {
      hasTriggeredInitialMessage.current = true;
      sendMessage(initialTopic.interactions[0].content, { isInitialMessage: true });
    }
  }, [initialTopic.interactions, sendMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isAgentWorking) return;

    // Ensure we're at bottom when sending new message
    setIsUserAtBottom(true);
    sendMessage(message);
    setMessage('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!message.trim() || isAgentWorking) return;
      handleSubmit(e);
    }
  };



  const documentViewer = useMemo(
    () => (
      <div
        className={cn(
          'relative h-full w-1/2 overflow-y-auto border-l bg-gray-50 dark:bg-gray-900',
          selectedReference ? 'block' : 'hidden'
        )}
      >
        <DocumentViewer
          url={selectedReference ? `${materialAccessUrl}/${selectedReference.key}` : ''}
          fileType={selectedReference?.fileType || DocumentType.PDF}
          options={{
            initialPage: selectedReference?.metadata?.pageNumber
              ? Number(selectedReference.metadata.pageNumber)
              : undefined,
            timestamp: selectedReference?.metadata?.timestamp,
          }}
        />
        <div
          className="absolute bottom-4 right-4 cursor-pointer rounded-full bg-black p-3 text-white opacity-80 shadow-lg transition-all duration-200 hover:bg-gray-700"
          onClick={() => setSelectedReference(null)}
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close document viewer</span>
        </div>
      </div>
    ),
    [selectedReference, setSelectedReference, materialAccessUrl]
  );

  return (
    <div className="flex h-[calc(100vh-4.1rem)] w-full overflow-hidden bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className={cn('flex flex-col', selectedReference ? 'w-1/2' : 'w-full')}>
        {/* Chat Header */}
        <div className="border-b bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm dark:bg-gray-900/80">
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                AI Assistant
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isAgentWorking ? 'Thinking...' : 'Ready to help with your documents'}
              </p>
            </div>
          </div>
        </div>

        <div ref={scrollContainerRef} className="flex-1 overflow-y-auto" onScroll={handleScroll}>
          <div className="space-y-6 p-6">
            {messages.map((msg, i) => {
              return (
                <div key={i}>
                  {/* User Message */}
                  {msg.senderType === 'user' && (
                    <div className="flex justify-end">
                      <div className="ml-auto max-w-[80%] space-y-1">
                        <div className="rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3 text-white shadow-lg transition-all duration-300 hover:shadow-xl">
                          <div className="whitespace-pre-wrap text-sm leading-relaxed">
                            {msg.content}
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {msg.createdAt ? formatTimestamp(msg.createdAt) : 'Just now'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* AI Message with Integrated Progress */}
                  {msg.senderType === 'ai' && (
                    <div className="space-y-3">
                      {/* Show Agent Progress Above AI Message */}
                      {((msg.agentSteps && msg.agentSteps.length > 0) ||
                        (i === messages.length - 1 && currentRunningStep)) && (
                        <div className="flex justify-start">
                          <div className="w-full md:w-[80%] lg:max-w-[55%]">
                            <AgentProgress
                              steps={msg.agentSteps || []}
                              isThinking={
                                msg.isStreamingContent === true ||
                                (i === messages.length - 1 && !!currentRunningStep)
                              }
                              currentRunningStep={
                                i === messages.length - 1 ? currentRunningStep : null
                              }
                              autoExpanded={true} // Always expanded by default
                            />
                          </div>
                        </div>
                      )}

                      {/* AI Message Content */}
                      {msg.content && (
                        <div className="flex justify-start">
                          <div className="max-w-[85%] space-y-2">
                            {/* AI Avatar and Header */}
                            <div className="flex items-center space-x-2">
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-md">
                                <Bot className="h-4 w-4 text-white" />
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                  AI Assistant
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {msg.createdAt ? formatTimestamp(msg.createdAt) : 'Just now'}
                                </span>
                              </div>
                            </div>

                            {/* Message Content */}
                            <div className="prose prose-sm max-w-none rounded-lg bg-gray-50 px-4 py-3 shadow-sm transition-all duration-300 dark:prose-invert dark:bg-gray-800 dark:text-gray-100">
                              <div className="[&_li]:my-1 [&_ol]:my-2 [&_p+ol]:mt-2 [&_p+ul]:mt-2 [&_p]:my-2 [&_ul]:my-2">
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm]}
                                  components={{
                                    // Enhanced markdown rendering with footnote support
                                    p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                                    strong: ({ children }) => (
                                      <strong className="font-semibold text-gray-900 dark:text-gray-100">
                                        {children}
                                      </strong>
                                    ),
                                  }}
                                >
                                  {msg.content}
                                </ReactMarkdown>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Error Display */}
            {error && (
              <div className="flex justify-start">
                <div className="max-w-[85%] rounded-2xl border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-red-500">
                      <X className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Error: {error.message}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Modern Input Area */}
        <div className="border-t bg-white p-4 pb-1 dark:bg-gray-900">
          <form onSubmit={handleSubmit}>
            <div className="mx-auto max-w-4xl">
              <div className="relative rounded-3xl border border-gray-200 bg-gray-50 shadow-sm transition-all duration-200 focus-within:border-gray-300 focus-within:shadow-md dark:border-gray-700 dark:bg-gray-800">
                <div className="flex items-end gap-2 p-3">
                  {/* Textarea with CSS auto-resize */}
                  <div className="flex-1">
                    <Textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={
                        isAgentWorking
                          ? 'AI is processing...'
                          : 'Ask me anything about your documents...'
                      }
                      disabled={isAgentWorking}
                      className={cn(
                        'min-h-[40px] max-h-40 w-full resize-none border-0 bg-transparent px-3 py-2 text-sm leading-6',
                        'placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0',
                        'dark:placeholder:text-gray-400',
                        isAgentWorking && 'cursor-not-allowed opacity-50'
                      )}
                      rows={1}
                      style={{
                        minHeight: '40px',
                        maxHeight: '160px', // 4 lines
                        overflow: 'hidden',
                        resize: 'none',
                      }}
                      onInput={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.height = 'auto';
                        target.style.height = `${Math.min(target.scrollHeight, 160)}px`;
                      }}
                    />

                    {/* Options positioned under textarea */}
                    <div className="mt-2 flex items-center gap-2">
                      {/* Add Button */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                        disabled={isAgentWorking}
                      >
                        <Plus className="mr-1 h-3 w-3" />
                        Add
                      </Button>

                      {/* Video Option */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                        disabled={isAgentWorking}
                      >
                        <Video className="mr-1 h-3 w-3" />
                        Video
                      </Button>

                      {/* Deep Research Option */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                        disabled={isAgentWorking}
                      >
                        <Search className="mr-1 h-3 w-3" />
                        Deep Research
                      </Button>

                      {/* Canvas Option */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                        disabled={isAgentWorking}
                      >
                        <Palette className="mr-1 h-3 w-3" />
                        Canvas
                      </Button>

                      {/* Settings Button */}
                      <Popover open={showSettings} onOpenChange={setShowSettings}>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 rounded-full text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                            disabled={isAgentWorking}
                          >
                            <Settings2 className="h-3 w-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80" align="end">
                          <ChatSettings />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Send Button */}
                  <Button
                    type="submit"
                    disabled={isAgentWorking || !message.trim()}
                    size="icon"
                    className={cn(
                      'h-8 w-8 shrink-0 rounded-full transition-all duration-200',
                      'bg-gray-900 hover:bg-gray-800 dark:bg-gray-100 dark:hover:bg-gray-200',
                      'disabled:bg-gray-300 disabled:cursor-not-allowed dark:disabled:bg-gray-600',
                      message.trim() && !isAgentWorking && 'shadow-sm hover:shadow-md'
                    )}
                  >
                    <Send className="h-4 w-4 text-white dark:text-gray-900" />
                  </Button>
                </div>
              </div>



              {/* Helper text */}
              <div className="mt-2 text-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Press Shift+Enter for new line
                </span>
              </div>
            </div>
          </form>
        </div>
      </div>

      {documentViewer}
    </div>
  );
}
